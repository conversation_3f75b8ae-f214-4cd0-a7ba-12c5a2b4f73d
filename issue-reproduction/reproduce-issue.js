import { initLogger } from 'braintrust';
import OpenAI from 'openai';

// Mock constants and enums
const AGENT_UNDERLORD_ID = 'test-agent-id';
const ActorType = {
    User: 'user'
};

const AnalyticsEventName = {
    agentApiResponded: 'agent_api_responded'
};

// Mock implementations
const Users = {
    isInternalUserEmail: (email) => email.includes('@braintrust.dev')
};

// <PERSON><PERSON> functions
async function getBraintrustLogger(request, agentId) {
    // Initialize a Braintrust logger
    const logger = initLogger({
        projectName: 'issue-reproduction',
        apiKey: process.env.BRAINTRUST_API_KEY || 'mock-api-key'
    });
    return logger;
}

function getLastUserQuery(messages) {
    return messages?.find(m => m.role === 'user')?.content || 'Hello, world!';
}

function extractActor(request) {
    return {
        type: ActorType.User,
        user: {
            email: '<EMAIL>'
        }
    };
}

async function createChatCompletion(payload, useBackups, useAlternativeKey) {
    // Mock OpenAI response
    return {
        id: 'chatcmpl-test',
        object: 'chat.completion',
        created: Date.now(),
        model: payload.model || 'gpt-3.5-turbo',
        choices: [{
            index: 0,
            message: {
                role: 'assistant',
                content: 'This is a mock response'
            },
            finish_reason: 'stop'
        }],
        usage: {
            prompt_tokens: 10,
            completion_tokens: 5,
            total_tokens: 15
        }
    };
}

async function handleSseStream(response, request, logger, analyticsContext) {
    // Mock SSE stream
    return new ReadableStream({
        start(controller) {
            controller.enqueue('data: {"choices":[{"delta":{"content":"Mock"}}]}\n\n');
            controller.enqueue('data: {"choices":[{"delta":{"content":" response"}}]}\n\n');
            controller.enqueue('data: [DONE]\n\n');
            controller.close();
        }
    });
}

function createAgentApiRespondedProps(baseProps, response) {
    return {
        ...baseProps,
        response_tokens: response.usage?.completion_tokens || 0,
        total_tokens: response.usage?.total_tokens || 0
    };
}

function addConversationHeaders(response, conversationEncodedSpan) {
    if (conversationEncodedSpan) {
        response.header('X-Conversation-Span', JSON.stringify(conversationEncodedSpan));
    }
    return response;
}

// Mock request/response objects
const mockRequest = {
    auth: {
        credentials: {
            id: 'test-user-id'
        }
    }
};

const mockResponse = {
    header: function(name, value) {
        console.log(`Header: ${name} = ${value}`);
        return this;
    },
    type: function(contentType) {
        console.log(`Content-Type: ${contentType}`);
        return this;
    }
};

const mockH = {
    response: function(data) {
        return {
            ...mockResponse,
            data: data,
            type: mockResponse.type,
            header: mockResponse.header
        };
    }
};

// Mock analytics
const analytics = {
    trackEvent: function(eventName, props) {
        console.log(`Analytics Event: ${eventName}`, props);
    }
};

// Main reproduction function - this is your original code with minimal changes
async function reproduceIssue() {
    console.log('Starting issue reproduction...');
    
    // Mock data
    const request = mockRequest;
    const h = mockH;
    const logger = console;
    const payload = {
        model: 'gpt-3.5-turbo',
        messages: [
            { role: 'user', content: 'Hello, how are you?' }
        ],
        stream: false
    };
    const useBackups = false;
    const useAlternativeOpenrouterKey = false;
    const conversationId = 'test-conversation-id';
    const turnId = 'test-turn-id';
    const requestId = 'test-request-id';
    const callSource = 'test-call-source';
    const project = {
        id: 'test-project-id',
        composition_id: 'test-composition-id',
        commit_ref: 'main'
    };
    const baseEventProps = {
        user_id: 'test-user-id',
        conversation_id: conversationId
    };
    
    let conversationEncodedSpan = null;

    // YOUR ORIGINAL CODE STARTS HERE (unchanged)
    try {
        const braintrustLogger = await getBraintrustLogger(request, AGENT_UNDERLORD_ID);

        let response;
        if (braintrustLogger) {
            const lastUserQuery = getLastUserQuery(payload.messages);
            const actor = extractActor(request);
            const isInternalUser =
                actor.type === ActorType.User && Users.isInternalUserEmail(actor.user.email);
            if (!conversationEncodedSpan) {
                const parentSpan = braintrustLogger.startSpan({
                    name: 'User turn',
                    spanId: turnId,
                    type: 'llm',
                    event: {
                        id: turnId,
                    },
                });
                parentSpan.log({
                    input: lastUserQuery,
                    metadata: {
                        user_id: request.auth.credentials.id,
                        conversation_id: conversationId,
                        turn_id: turnId,
                        project_id: project?.id,
                        composition_id: project?.composition_id,
                        commit_ref: project?.commit_ref,
                        model: payload.model,
                        is_internal: isInternalUser,
                    },
                });
                conversationEncodedSpan = await parentSpan.export();
            }

            const callSourceSpan = braintrustLogger.startSpan({
                name: callSource ? `Parent - ${callSource}` : 'unknown_call_source',
                spanId: callSource ?? 'unknown_call_source',
                parent: conversationEncodedSpan,
                event: {
                    id: requestId,
                },
            });
            callSourceSpan.end();

            response = await braintrustLogger.traced(
                async () => {
                    return await createChatCompletion(
                        payload,
                        useBackups,
                        useAlternativeOpenrouterKey,
                    );
                },
                {
                    name: callSource ? `Child - ${callSource}` : 'LLM',
                    type: 'llm',
                    event: {
                        id: requestId,
                        input: lastUserQuery,
                        metadata: {
                            conversation_id: conversationId,
                            turn_id: turnId,
                            request_id: requestId,
                            project_id: project?.id,
                            composition_id: project?.composition_id,
                            commit_ref: project?.commit_ref,
                            model: payload.model,
                            user_id: request.auth.credentials.id,
                            is_internal: isInternalUser,
                        },
                    },
                    parentSpanIds: {
                        rootSpanId: turnId,
                        spanId: callSource ?? 'unknown_call_source',
                    },
                },
            );
        } else {
            response = await createChatCompletion(
                payload,
                useBackups,
                useAlternativeOpenrouterKey,
            );
        }
        if (payload.stream && Symbol.asyncIterator in response) {
            // TODO: [AVE-1265] Remove the callSource check after the client changes have been deployed
            const analyticsContext = callSource
                ? {
                      baseEventProps,
                      analytics,
                  }
                : undefined;

            // Stream the response to the client
            const sseStream = await handleSseStream(
                response,
                request,
                logger,
                analyticsContext,
            );

            return addConversationHeaders(
                h
                    .response(sseStream)
                    .type('text/event-stream')
                    // X-Accel-Buffering=no instructs nginx to disable buffering for this response
                    .header('X-Accel-Buffering', 'no'),
                conversationEncodedSpan,
            );
        }

        // Track agent_api_responded for non-streaming responses
        // TODO: [AVE-1265] Remove the callSource check after the client changes have been deployed
        if (callSource) {
            const responseProps = createAgentApiRespondedProps(
                baseEventProps,
                response,
            );
            analytics.trackEvent(AnalyticsEventName.agentApiResponded, responseProps);
        }

        return addConversationHeaders(h.response(response), conversationEncodedSpan);
    } catch (error) {
        console.error('Error during reproduction:', error);
        throw error;
    }
    // YOUR ORIGINAL CODE ENDS HERE
}

// Run the reproduction
reproduceIssue()
    .then(result => {
        console.log('Reproduction completed successfully');
        console.log('Result:', result);
    })
    .catch(error => {
        console.error('Reproduction failed:', error);
        process.exit(1);
    });
