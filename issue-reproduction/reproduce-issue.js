#!/usr/bin/env node

// <PERSON><PERSON> script to reproduce the issue from the provided code extract
// This preserves the original logic flow without changing the code structure

const { initLogger } = require('../js/dist/index.js');

// Initialize Braintrust logger
initLogger({ projectName: 'issue-reproduction' });

// Mock constants and types
const AGENT_UNDERLORD_ID = 'mock-agent-id';

const ActorType = {
  User: 'user',
  System: 'system'
};

const AnalyticsEventName = {
  agentApiResponded: 'agent_api_responded'
};

// Mock classes and functions
class Users {
  static isInternalUserEmail(email) {
    return email && email.includes('@internal.com');
  }
}

// Mock getBraintrustLogger function
async function getBraintrustLogger(request, agentId) {
  // Simulate sometimes returning null (no logger available)
  if (Math.random() < 0.3) {
    return null;
  }
  
  // Mock Braintrust logger with the methods used in the original code
  return {
    startSpan: (config) => {
      console.log(`Starting span: ${config.name} (ID: ${config.spanId})`);
      return {
        log: (data) => {
          console.log(`Logging to span ${config.spanId}:`, JSON.stringify(data, null, 2));
        },
        export: async () => {
          console.log(`Exporting span: ${config.spanId}`);
          return `encoded-span-${config.spanId}`;
        },
        end: () => {
          console.log(`Ending span: ${config.spanId}`);
        }
      };
    },
    traced: async (fn, config) => {
      console.log(`Starting traced operation: ${config.name}`);
      console.log(`Parent span IDs:`, config.parentSpanIds);
      const result = await fn();
      console.log(`Completed traced operation: ${config.name}`);
      return result;
    }
  };
}

function getLastUserQuery(messages) {
  if (!messages || messages.length === 0) return 'No messages';
  const lastMessage = messages[messages.length - 1];
  return lastMessage.content || lastMessage.message || 'Empty message';
}

function extractActor(request) {
  return {
    type: ActorType.User,
    user: {
      email: request.auth?.credentials?.email || '<EMAIL>'
    }
  };
}

async function createChatCompletion(payload, useBackups, useAlternativeKey) {
  console.log(`Creating chat completion with model: ${payload.model}`);
  console.log(`Stream: ${payload.stream}, Backups: ${useBackups}, Alt key: ${useAlternativeKey}`);
  
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  if (payload.stream) {
    // Mock streaming response
    return {
      [Symbol.asyncIterator]: async function* () {
        for (let i = 0; i < 3; i++) {
          yield { choices: [{ delta: { content: `chunk ${i}` } }] };
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
    };
  } else {
    // Mock non-streaming response
    return {
      choices: [{ message: { content: 'Mock response' } }],
      usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 }
    };
  }
}

async function handleSseStream(response, request, logger, analyticsContext) {
  console.log('Handling SSE stream');
  return 'mock-sse-stream';
}

function addConversationHeaders(response, conversationEncodedSpan) {
  console.log(`Adding conversation headers with span: ${conversationEncodedSpan}`);
  return response;
}

function createAgentApiRespondedProps(baseEventProps, response) {
  return {
    ...baseEventProps,
    response_tokens: response.usage?.completion_tokens || 0,
    total_tokens: response.usage?.total_tokens || 0
  };
}

// Mock request and response objects
const mockRequest = {
  auth: {
    credentials: {
      id: 'user-123',
      email: '<EMAIL>'
    }
  }
};

const mockPayload = {
  messages: [
    { role: 'user', content: 'Hello, how are you?' }
  ],
  model: 'gpt-4',
  stream: false
};

const mockProject = {
  id: 'project-123',
  composition_id: 'comp-456',
  commit_ref: 'abc123'
};

// Mock response object (Hapi.js style)
const h = {
  response: (data) => ({
    type: (contentType) => ({
      header: (name, value) => {
        console.log(`Setting header ${name}: ${value}`);
        return { data, contentType, headers: { [name]: value } };
      }
    }),
    data
  })
};

const mockLogger = console;
const mockAnalytics = {
  trackEvent: (eventName, props) => {
    console.log(`Analytics event: ${eventName}`, props);
  }
};

// Variables that would be available in the original context
let conversationEncodedSpan = null;
const conversationId = 'conv-789';
const turnId = 'turn-456';
const requestId = 'req-123';
const callSource = 'test-source';
const useBackups = false;
const useAlternativeOpenrouterKey = false;
const baseEventProps = { timestamp: Date.now() };
const analytics = mockAnalytics;
const request = mockRequest;
const payload = mockPayload;
const project = mockProject;
const logger = mockLogger;

// Main function that reproduces the issue
async function reproduceIssue() {
  console.log('=== Starting Issue Reproduction ===\n');
  
  try {
    // This is the exact code from your extract - DO NOT CHANGE
    const braintrustLogger = await getBraintrustLogger(request, AGENT_UNDERLORD_ID);

    let response;
    if (braintrustLogger) {
        const lastUserQuery = getLastUserQuery(payload.messages);
        const actor = extractActor(request);
        const isInternalUser =
            actor.type === ActorType.User && Users.isInternalUserEmail(actor.user.email);
        if (!conversationEncodedSpan) {
            const parentSpan = braintrustLogger.startSpan({
                name: 'User turn',
                spanId: turnId,
                type: 'llm',
                event: {
                    id: turnId,
                },
            });
            parentSpan.log({
                input: lastUserQuery,
                metadata: {
                    user_id: request.auth.credentials.id,
                    conversation_id: conversationId,
                    turn_id: turnId,
                    project_id: project?.id,
                    composition_id: project?.composition_id,
                    commit_ref: project?.commit_ref,
                    model: payload.model,
                    is_internal: isInternalUser,
                },
            });
            conversationEncodedSpan = await parentSpan.export();
        }

        const callSourceSpan = braintrustLogger.startSpan({
            name: callSource ? `Parent - ${callSource}` : 'unknown_call_source',
            spanId: callSource ?? 'unknown_call_source',
            parent: conversationEncodedSpan,
            event: {
                id: requestId,
            },
        });
        callSourceSpan.end();

        response = await braintrustLogger.traced(
            async () => {
                return await createChatCompletion(
                    payload,
                    useBackups,
                    useAlternativeOpenrouterKey,
                );
            },
            {
                name: callSource ? `Child - ${callSource}` : 'LLM',
                type: 'llm',
                event: {
                    id: requestId,
                    input: lastUserQuery,
                    metadata: {
                        conversation_id: conversationId,
                        turn_id: turnId,
                        request_id: requestId,
                        project_id: project?.id,
                        composition_id: project?.composition_id,
                        commit_ref: project?.commit_ref,
                        model: payload.model,
                        user_id: request.auth.credentials.id,
                        is_internal: isInternalUser,
                    },
                },
                parentSpanIds: {
                    rootSpanId: turnId,
                    spanId: callSource ?? 'unknown_call_source',
                },
            },
        );
    } else {
        response = await createChatCompletion(
            payload,
            useBackups,
            useAlternativeOpenrouterKey,
        );
    }

    if (payload.stream && Symbol.asyncIterator in response) {
        // TODO: [AVE-1265] Remove the callSource check after the client changes have been deployed
        const analyticsContext = callSource
            ? {
                  baseEventProps,
                  analytics,
              }
            : undefined;

        // Stream the response to the client
        const sseStream = await handleSseStream(
            response,
            request,
            logger,
            analyticsContext,
        );

        return addConversationHeaders(
            h
                .response(sseStream)
                .type('text/event-stream')
                // X-Accel-Buffering=no instructs nginx to disable buffering for this response
                .header('X-Accel-Buffering', 'no'),
            conversationEncodedSpan,
        );
    }

    // Track agent_api_responded for non-streaming responses
    // TODO: [AVE-1265] Remove the callSource check after the client changes have been deployed
    if (callSource) {
        const responseProps = createAgentApiRespondedProps(
            baseEventProps,
            response,
        );
        analytics.trackEvent(AnalyticsEventName.agentApiResponded, responseProps);
    }

    return addConversationHeaders(h.response(response), conversationEncodedSpan);
    
  } catch (error) {
    console.error('Error during reproduction:', error);
    throw error;
  }
}

// Run the reproduction
async function main() {
  try {
    console.log('Running with non-streaming response...\n');
    await reproduceIssue();
    
    console.log('\n=== Running with streaming response ===\n');
    payload.stream = true;
    await reproduceIssue();
    
    console.log('\n=== Running without Braintrust logger ===\n');
    // Force getBraintrustLogger to return null
    const originalGetBraintrustLogger = getBraintrustLogger;
    global.getBraintrustLogger = async () => null;
    await reproduceIssue();
    
  } catch (error) {
    console.error('Reproduction failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { reproduceIssue };
